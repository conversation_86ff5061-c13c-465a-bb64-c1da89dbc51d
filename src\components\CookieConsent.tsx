'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON>, Setting<PERSON>, <PERSON>ie } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  advertising: boolean;
  social: boolean;
}

const defaultPreferences: CookiePreferences = {
  necessary: true, // Always true, cannot be disabled
  analytics: false,
  advertising: false,
  social: false,
};

export function CookieConsent() {
  const [showBanner, setShowBanner] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>(defaultPreferences);

  useEffect(() => {
    // Check if user has already made a choice
    const consent = localStorage.getItem('cookie-consent');
    if (!consent) {
      // Show banner after a short delay
      const timer = setTimeout(() => setShowBanner(true), 2000);
      return () => clearTimeout(timer);
    } else {
      // Load saved preferences
      try {
        const savedPreferences = JSON.parse(consent);
        setPreferences(savedPreferences);
      } catch (error) {
        console.error('Error parsing cookie preferences:', error);
      }
    }
  }, []);

  const savePreferences = (prefs: CookiePreferences) => {
    localStorage.setItem('cookie-consent', JSON.stringify(prefs));
    setPreferences(prefs);
    setShowBanner(false);
    setShowSettings(false);
    
    // Apply preferences
    applyPreferences(prefs);
  };

  const applyPreferences = (prefs: CookiePreferences) => {
    // Analytics
    if (prefs.analytics) {
      // Enable Google Analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('consent', 'update', {
          analytics_storage: 'granted'
        });
      }
    } else {
      // Disable Google Analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('consent', 'update', {
          analytics_storage: 'denied'
        });
      }
    }

    // Advertising
    if (prefs.advertising) {
      // Enable advertising cookies
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('consent', 'update', {
          ad_storage: 'granted',
          ad_user_data: 'granted',
          ad_personalization: 'granted'
        });
      }
    } else {
      // Disable advertising cookies
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('consent', 'update', {
          ad_storage: 'denied',
          ad_user_data: 'denied',
          ad_personalization: 'denied'
        });
      }
    }
  };

  const acceptAll = () => {
    const allAccepted: CookiePreferences = {
      necessary: true,
      analytics: true,
      advertising: true,
      social: true,
    };
    savePreferences(allAccepted);
  };

  const acceptNecessaryOnly = () => {
    savePreferences(defaultPreferences);
  };

  const updatePreference = (key: keyof CookiePreferences, value: boolean) => {
    if (key === 'necessary') return; // Cannot disable necessary cookies
    setPreferences(prev => ({ ...prev, [key]: value }));
  };

  if (!showBanner) return null;

  return (
    <>
      {/* Cookie Consent Banner */}
      <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-background/95 backdrop-blur border-t shadow-lg">
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col lg:flex-row items-start lg:items-center gap-4">
              <div className="flex items-start gap-3 flex-1">
                <Cookie className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                <div className="flex-1">
                  <h3 className="font-bold text-sm mb-2">نحن نستخدم ملفات تعريف الارتباط (الكوكيز)</h3>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    نستخدم الكوكيز لتحسين تجربتك وتقديم محتوى مخصص وتحليل حركة المرور. 
                    بعض الكوكيز ضرورية لعمل الموقع، بينما أخرى اختيارية.{' '}
                    <Link href="/p/cookies" className="text-primary hover:underline">
                      اعرف المزيد
                    </Link>
                  </p>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-2 w-full lg:w-auto">
                <Dialog open={showSettings} onOpenChange={setShowSettings}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" className="w-full sm:w-auto">
                      <Settings className="h-4 w-4 ml-2" />
                      إعدادات الكوكيز
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md">
                    <DialogHeader>
                      <DialogTitle>إعدادات ملفات تعريف الارتباط</DialogTitle>
                      <DialogDescription>
                        اختر أنواع الكوكيز التي تريد السماح بها
                      </DialogDescription>
                    </DialogHeader>
                    
                    <div className="space-y-4">
                      {/* Necessary Cookies */}
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="font-medium">الكوكيز الضرورية</Label>
                          <p className="text-xs text-muted-foreground">
                            مطلوبة لعمل الموقع بشكل صحيح
                          </p>
                        </div>
                        <Switch checked={true} disabled />
                      </div>
                      
                      {/* Analytics Cookies */}
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="font-medium">كوكيز التحليل</Label>
                          <p className="text-xs text-muted-foreground">
                            تساعدنا في فهم كيفية استخدام الموقع
                          </p>
                        </div>
                        <Switch 
                          checked={preferences.analytics}
                          onCheckedChange={(checked) => updatePreference('analytics', checked)}
                        />
                      </div>
                      
                      {/* Advertising Cookies */}
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="font-medium">كوكيز الإعلانات</Label>
                          <p className="text-xs text-muted-foreground">
                            لعرض إعلانات مناسبة لاهتماماتك
                          </p>
                        </div>
                        <Switch 
                          checked={preferences.advertising}
                          onCheckedChange={(checked) => updatePreference('advertising', checked)}
                        />
                      </div>
                      
                      {/* Social Media Cookies */}
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label className="font-medium">كوكيز وسائل التواصل</Label>
                          <p className="text-xs text-muted-foreground">
                            لمشاركة المحتوى على وسائل التواصل
                          </p>
                        </div>
                        <Switch 
                          checked={preferences.social}
                          onCheckedChange={(checked) => updatePreference('social', checked)}
                        />
                      </div>
                    </div>
                    
                    <div className="flex gap-2 mt-6">
                      <Button 
                        onClick={() => savePreferences(preferences)}
                        className="flex-1"
                      >
                        حفظ الإعدادات
                      </Button>
                      <Button 
                        variant="outline" 
                        onClick={() => setShowSettings(false)}
                      >
                        إلغاء
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={acceptNecessaryOnly}
                  className="w-full sm:w-auto"
                >
                  الضرورية فقط
                </Button>
                
                <Button 
                  size="sm" 
                  onClick={acceptAll}
                  className="w-full sm:w-auto"
                >
                  قبول الكل
                </Button>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowBanner(false)}
                className="absolute top-2 left-2 lg:relative lg:top-0 lg:left-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}

// Extend Window interface for gtag
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}
