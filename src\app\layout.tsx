import type { Metadata } from 'next';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';
import { cn } from '@/lib/utils';
import { SidebarProvider } from '@/components/ui/sidebar';
import { Header } from '@/components/Header';
import { AppSidebar } from '@/components/AppSidebar';
import { Footer } from '@/components/Footer';
import { JsonLd } from '@/components/JsonLd';
import { GoogleAnalytics } from '@/components/GoogleAnalytics';
import { AutoAds } from '@/components/AdSense';
import { CookieConsent } from '@/components/CookieConsent';
import type { WebSite } from 'schema-dts';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;
const gaId = process.env.NEXT_PUBLIC_GA_ID;
const adSenseClient = process.env.NEXT_PUBLIC_ADSENSE_CLIENT;
const googleSiteVerification = process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION;

export const metadata: Metadata = {
  metadataBase: siteUrl ? new URL(siteUrl) : undefined,
  title: {
    default: 'أدوات بالعربي - مجموعة أدوات عربية شاملة ومجانية 2025',
    template: '%s | أدوات بالعربي',
  },
  description: 'مجموعة شاملة من الأدوات والحاسبات والمحولات المجانية باللغة العربية. محول التاريخ الهجري، حاسبة الزكاة، أدوات النصوص، والمزيد من الأدوات المفيدة لتسهيل حياتك اليومية.',
  keywords: [
    'أدوات عربية', 'حاسبة عربية', 'محول التاريخ', 'محول هجري ميلادي',
    'حاسبة الزكاة', 'أدوات مجانية', 'مجموعة أدوات', 'حاسبة العمر',
    'عداد الكلمات', 'محول العملات', 'أدوات النصوص', 'حاسبة مالية',
    'أدوات رقمية', 'موقع أدوات', 'حاسبات مجانية', 'محولات مجانية',
    'أدوات إسلامية', 'حاسبة شرعية', 'تطبيقات عربية', 'مواقع عربية'
  ],
  openGraph: {
    title: 'أدوات بالعربي - مجموعة أدوات عربية شاملة ومجانية',
    description: 'مجموعة شاملة من الأدوات والحاسبات والمحولات المجانية باللغة العربية. محول التاريخ الهجري، حاسبة الزكاة، أدوات النصوص، والمزيد من الأدوات المفيدة.',
    url: siteUrl,
    siteName: 'أدوات بالعربي',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'أدوات بالعربي - مجموعة أدوات عربية شاملة ومجانية',
      },
    ],
    locale: 'ar_SA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'أدوات بالعربي - مجموعة أدوات عربية شاملة ومجانية',
    description: 'مجموعة شاملة من الأدوات والحاسبات والمحولات المجانية باللغة العربية. محول التاريخ الهجري، حاسبة الزكاة، أدوات النصوص، والمزيد.',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: googleSiteVerification,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const jsonLd: WebSite | null = siteUrl ? {
    '@type': 'WebSite',
    name: 'أدوات بالعربي',
    url: siteUrl,
    inLanguage: 'ar',
    description: 'مجموعة شاملة من الأدوات والحاسبات والمحولات باللغة العربية لتسهيل حياتك اليومية.',
    publisher: {
      '@type': 'Organization',
      name: 'أدوات بالعربي',
      logo: {
        '@type': 'ImageObject',
        url: `${siteUrl}/logo.png`,
      }
    }
  } : null;

  return (
    <html lang="ar" dir="rtl" className="h-full">
      <head>
        {jsonLd && <JsonLd data={jsonLd} />}
        <link rel="manifest" href="/manifest.json" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/icon-192.png" />
        <meta name="theme-color" content="#2563eb" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Wafeq:wght@600&display=swap" rel="stylesheet" />
      </head>
      <body className={cn('font-body antialiased min-h-screen bg-background w-full h-full')}>
        <SidebarProvider>
          <div className="relative flex min-h-screen w-full flex-col">
            <Header />
            <div className="flex flex-1 w-full">
               <AppSidebar />
               <main className="flex flex-1 flex-col w-full">
                {children}
                <Footer />
              </main>
            </div>
          </div>
        </SidebarProvider>
        <Toaster />
        <CookieConsent />
        {gaId && <GoogleAnalytics gaId={gaId} />}
        {adSenseClient && <AutoAds adClient={adSenseClient} />}
      </body>
    </html>
  );
}
