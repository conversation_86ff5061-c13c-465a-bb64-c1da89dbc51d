# دليل إعداد Google AdSense - أدوات بالعربي

هذا الدليل يوضح الخطوات المطلوبة لإعداد موقع "أدوات بالعربي" للحصول على موافقة Google AdSense.

## ✅ المتطلبات المكتملة

### 1. الصفحات المطلوبة
- [x] **سياسة الخصوصية** (`/p/privacy-policy`)
- [x] **شروط الخدمة** (`/p/terms-of-service`)
- [x] **صفحة اتصل بنا** (`/p/contact`)
- [x] **صفحة حول الموقع** (`/p/about`)
- [x] **إخلاء المسؤولية** (`/disclaimer`)
- [x] **سياسة الكوكيز** (`/p/cookies`)
- [x] **نظام موافقة الكوكيز (GDPR)**

### 2. المتطلبات التقنية
- [x] **ملف ads.txt** (`/public/ads.txt`)
- [x] **ملف robots.txt محسن** (`/public/robots.txt`)
- [x] **خريطة الموقع XML** (`/sitemap.xml`)
- [x] **ملف manifest.json** (`/public/manifest.json`)
- [x] **تحسين SEO شامل**
- [x] **تصميم متجاوب**
- [x] **تحسين سرعة التحميل**

### 3. المحتوى والتجربة
- [x] **محتوى أصلي ومفيد**
- [x] **مقالات تعليمية**
- [x] **أدوات تفاعلية متنوعة**
- [x] **تجربة مستخدم ممتازة**
- [x] **واجهة عربية كاملة**

### 4. التتبع والتحليل
- [x] **Google Analytics مُعد**
- [x] **مكونات AdSense جاهزة**
- [x] **تتبع الأداء**

### 5. الامتثال لـ GDPR والخصوصية
- [x] **سياسة كوكيز شاملة**
- [x] **نظام موافقة الكوكيز**
- [x] **Google Consent Mode مُفعل**
- [x] **حقوق المستخدمين محددة**
- [x] **إعدادات الخصوصية قابلة للتخصيص**

## 🔧 خطوات الإعداد النهائية

### الخطوة 1: إعداد متغيرات البيئة

أنشئ ملف `.env.local` وأضف المتغيرات التالية:

```env
# إعدادات الموقع الأساسية
NEXT_PUBLIC_SITE_URL=https://tools.tolabi.net

# Google Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Google AdSense (سيتم الحصول عليه بعد الموافقة)
NEXT_PUBLIC_ADSENSE_CLIENT=ca-pub-XXXXXXXXXXXXXXXXX

# Google Search Console
NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION=your_verification_code
```

### الخطوة 2: إعداد Google Analytics

1. اذهب إلى [Google Analytics](https://analytics.google.com/)
2. أنشئ خاصية GA4 جديدة
3. احصل على معرف القياس (G-XXXXXXXXXX)
4. أضفه إلى `.env.local`

### الخطوة 3: إعداد Google Search Console

1. اذهب إلى [Google Search Console](https://search.google.com/search-console/)
2. أضف موقعك
3. تحقق من الملكية باستخدام HTML tag
4. أضف رمز التحقق إلى `.env.local`

### الخطوة 4: تحديث ملف ads.txt

1. افتح `public/ads.txt`
2. استبدل `pub-XXXXXXXXXXXXXXXXX` برقم الناشر الخاص بك من AdSense
3. احفظ الملف

### الخطوة 5: اختبار نظام الكوكيز

1. تأكد من ظهور بانر الكوكيز عند الزيارة الأولى
2. اختبر إعدادات الكوكيز المختلفة
3. تحقق من عمل Google Consent Mode
4. تأكد من حفظ التفضيلات

### الخطوة 6: رفع الموقع للإنتاج

```bash
# بناء المشروع
npm run build

# رفع إلى الخادم
# (حسب منصة الاستضافة المستخدمة)
```

## 📋 قائمة التحقق قبل التقديم لـ AdSense

### المحتوى والجودة
- [ ] الموقع يحتوي على محتوى أصلي ومفيد
- [ ] لا يوجد محتوى منسوخ أو مكرر
- [ ] المقالات والأدوات تقدم قيمة حقيقية للمستخدمين
- [ ] اللغة العربية صحيحة ومفهومة
- [ ] لا يوجد أخطاء إملائية أو نحوية كبيرة

### التصميم وتجربة المستخدم
- [ ] التصميم احترافي ومتناسق
- [ ] التنقل سهل وواضح
- [ ] الموقع يعمل بشكل صحيح على الجوال
- [ ] سرعة التحميل جيدة (أقل من 3 ثوان)
- [ ] لا توجد روابط مكسورة

### المتطلبات التقنية
- [ ] جميع الصفحات المطلوبة موجودة ومكتملة
- [ ] ملف ads.txt صحيح ومحدث
- [ ] خريطة الموقع تعمل بشكل صحيح
- [ ] Google Analytics مثبت ويعمل
- [ ] Google Search Console مُعد

### متطلبات GDPR والخصوصية
- [ ] سياسة الكوكيز مكتملة ومفصلة
- [ ] نظام موافقة الكوكيز يعمل بشكل صحيح
- [ ] Google Consent Mode مُفعل
- [ ] المستخدمون يمكنهم تخصيص إعدادات الكوكيز
- [ ] حقوق المستخدمين محددة بوضوح
- [ ] إمكانية سحب الموافقة متاحة

### حركة المرور
- [ ] الموقع يحصل على زوار حقيقيين
- [ ] مصادر الزوار متنوعة (بحث، مباشر، إحالة)
- [ ] معدل الارتداد معقول (أقل من 70%)
- [ ] متوسط وقت الجلسة جيد (أكثر من دقيقة)

## 🚀 خطوات التقديم لـ AdSense

### 1. إنشاء حساب AdSense
1. اذهب إلى [Google AdSense](https://www.google.com/adsense/)
2. انقر على "البدء"
3. أدخل رابط موقعك: `https://tools.tolabi.net`
4. اختر بلدك (السعودية)
5. اختر نوع الدفع المفضل

### 2. ربط الموقع
1. أضف كود AdSense إلى موقعك (تلقائي عبر المتغيرات)
2. انتظر 24-48 ساعة للفحص الأولي
3. تأكد من ظهور "جاري المراجعة" في لوحة التحكم

### 3. انتظار المراجعة
- المراجعة قد تستغرق من أسبوع إلى شهر
- تحقق من بريدك الإلكتروني بانتظام
- لا تقم بتغييرات كبيرة أثناء المراجعة

## 💡 نصائح لزيادة فرص القبول

### المحتوى
1. **أضف محتوى بانتظام**: انشر مقالات جديدة أسبوعياً
2. **حسن الأدوات الموجودة**: أضف ميزات جديدة للأدوات
3. **اكتب مقالات شاملة**: مقالات طويلة ومفيدة تحصل على تقييم أفضل

### التسويق
1. **شارك في وسائل التواصل**: انشر روابط الموقع في المجموعات المناسبة
2. **تحسين SEO**: استخدم كلمات مفتاحية مناسبة
3. **بناء روابط خلفية**: احصل على روابط من مواقع موثوقة

### التحليل
1. **راقب Google Analytics**: تابع سلوك الزوار
2. **حسن الصفحات الضعيفة**: الصفحات ذات معدل الارتداد العالي
3. **اختبر سرعة الموقع**: استخدم PageSpeed Insights

## ⚠️ أخطاء شائعة يجب تجنبها

1. **المحتوى المنسوخ**: لا تنسخ محتوى من مواقع أخرى
2. **الإعلانات المزيفة**: لا تضع إعلانات وهمية
3. **النقر الذاتي**: لا تنقر على إعلاناتك بنفسك
4. **المحتوى المحظور**: تجنب المحتوى الحساس أو المخالف
5. **الصفحات الفارغة**: تأكد من أن جميع الصفحات مكتملة

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل:

1. **مراجعة الوثائق**: [مركز مساعدة AdSense](https://support.google.com/adsense/)
2. **منتديات AdSense**: [منتدى مساعدة AdSense](https://support.google.com/adsense/community)
3. **التواصل معنا**: عبر صفحة اتصل بنا في الموقع

## 🎉 بعد الموافقة

عند الحصول على الموافقة:

1. **حدث ملف ads.txt** برقم الناشر الصحيح
2. **فعل الإعلانات** من لوحة تحكم AdSense
3. **راقب الأداء** وحسن مواضع الإعلانات
4. **التزم بالسياسات** لتجنب إيقاف الحساب

---

**ملاحظة**: هذا الدليل محدث حتى ديسمبر 2024. قد تتغير متطلبات AdSense مع الوقت.
