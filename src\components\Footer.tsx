import Link from 'next/link';

export function Footer() {
  return (
    <footer className="w-full border-t bg-card text-card-foreground">
      <div className="container px-4 py-8 md:px-6">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* About Section */}
          <div className="md:col-span-2">
            <h3 className="font-headline font-bold text-lg mb-4">أدوات بالعربي</h3>
            <p className="text-sm text-muted-foreground mb-4 leading-relaxed">
              مجموعة شاملة من الأدوات والحاسبات والمحولات المجانية باللغة العربية.
              نهدف إلى تسهيل مهامك اليومية والأكاديمية والمالية من خلال أدوات عملية وسهلة الاستخدام.
            </p>
            <div className="flex flex-wrap gap-2">
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">مجاني</span>
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">آمن</span>
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">سريع</span>
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">عربي</span>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-bold text-sm mb-4">روابط سريعة</h4>
            <nav className="flex flex-col gap-2 text-sm">
              <Link href="/" className="text-muted-foreground hover:text-primary transition-colors">
                الصفحة الرئيسية
              </Link>
              <Link href="/articles" className="text-muted-foreground hover:text-primary transition-colors">
                المقالات
              </Link>
              <Link href="/sitemap-page" className="text-muted-foreground hover:text-primary transition-colors">
                خريطة الموقع
              </Link>
              <Link href="/p/about" className="text-muted-foreground hover:text-primary transition-colors">
                حول الموقع
              </Link>
              <Link href="/p/contact" className="text-muted-foreground hover:text-primary transition-colors">
                اتصل بنا
              </Link>
            </nav>
          </div>

          {/* Legal Links */}
          <div>
            <h4 className="font-bold text-sm mb-4">الصفحات القانونية</h4>
            <nav className="flex flex-col gap-2 text-sm">
              <Link href="/p/privacy-policy" className="text-muted-foreground hover:text-primary transition-colors">
                سياسة الخصوصية
              </Link>
              <Link href="/p/terms-of-service" className="text-muted-foreground hover:text-primary transition-colors">
                شروط الخدمة
              </Link>
              <Link href="/disclaimer" className="text-muted-foreground hover:text-primary transition-colors">
                إخلاء المسؤولية
              </Link>
              <Link href="/p/cookies" className="text-muted-foreground hover:text-primary transition-colors">
                سياسة الكوكيز
              </Link>
            </nav>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t pt-6 flex flex-col md:flex-row items-center justify-between gap-4">
          <p className="text-sm text-muted-foreground text-center md:text-right">
            &copy; {new Date().getFullYear()} أدوات بالعربي. جميع الحقوق محفوظة.
          </p>

          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <span>آخر تحديث: {new Date().toLocaleDateString('ar-SA')}</span>
            <span>•</span>
            <span>صُنع بـ ❤️ للمجتمع العربي</span>
          </div>
        </div>
      </div>
    </footer>
  );
}
